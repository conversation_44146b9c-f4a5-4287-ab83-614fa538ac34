#!/usr/bin/env python3
"""
Performance Optimizations Test Suite
Tests all the implemented performance optimizations:
1. Parallel Mutation Generation
2. Batch Fitness Evaluation with GPU
3. Parallel Pruning Comparison
4. Asynchronous Visualization Rendering
5. Lightweight Serialization
"""

import sys
import os
import time
import numpy as np
from datetime import datetime

# Add backend to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_parallel_mutation_generation():
    """Test parallel mutation generation with ProcessPoolExecutor"""
    print("🧪 Testing Parallel Mutation Generation...")
    
    try:
        from backend.simulator.mutation_engine import MutationEngine
        
        # Create test sequence
        test_sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
        
        # Initialize mutation engine with parallel processing
        engine = MutationEngine(
            reference_sequence=test_sequence,
            mutation_rate=0.001,
            use_gpu=True,
            max_workers=4
        )
        
        # Test parallel generation
        start_time = time.time()
        results = engine.run_simulation(
            max_generations=3,
            branches_per_node=3,
            pruning_method="top_k",
            pruning_threshold=10,
            use_parallel=True
        )
        parallel_time = time.time() - start_time
        
        # Test sequential generation
        start_time = time.time()
        results_seq = engine.run_simulation(
            max_generations=3,
            branches_per_node=3,
            pruning_method="top_k",
            pruning_threshold=10,
            use_parallel=False
        )
        sequential_time = time.time() - start_time
        
        print(f"✅ Parallel generation: {parallel_time:.2f}s")
        print(f"✅ Sequential generation: {sequential_time:.2f}s")
        print(f"🚀 Speedup: {sequential_time/parallel_time:.2f}x")
        
        return True
        
    except Exception as e:
        print(f"❌ Parallel mutation generation test failed: {e}")
        return False

def test_batch_fitness_evaluation():
    """Test batch fitness evaluation with GPU optimization"""
    print("🧪 Testing Batch Fitness Evaluation...")
    
    try:
        from backend.simulator.mutation_engine import MutationEngine
        
        # Create test sequence
        test_sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
        
        # Initialize mutation engine
        engine = MutationEngine(
            reference_sequence=test_sequence,
            mutation_rate=0.001,
            use_gpu=True
        )
        
        # Create test nodes
        test_nodes = []
        for i in range(10):
            mutations = engine.generate_mutations(test_sequence)
            new_sequence = engine.apply_mutations(test_sequence, mutations)
            
            # Create mock node
            from backend.simulator.mutation_engine import MutationNode
            node = MutationNode(
                id=f"test_node_{i}",
                sequence=new_sequence,
                parent_id="root",
                mutations=mutations,
                fitness=0.5,
                generation=1,
                timestamp=datetime.now()
            )
            test_nodes.append(node)
        
        # Test batch fitness evaluation
        gpu_time = 0
        if engine.gpu_available:
            start_time = time.time()
            batch_fitness = engine._batch_fitness_evaluation_gpu(test_nodes)
            gpu_time = time.time() - start_time
            print(f"✅ GPU batch evaluation: {gpu_time:.4f}s for {len(test_nodes)} nodes")
        
        # Test individual fitness evaluation
        start_time = time.time()
        individual_fitness = []
        for node in test_nodes:
            fitness = engine.calculate_fitness(node.sequence, node.mutations)
            individual_fitness.append(fitness)
        individual_time = time.time() - start_time
        
        print(f"✅ Individual evaluation: {individual_time:.4f}s for {len(test_nodes)} nodes")
        
        if engine.gpu_available and gpu_time > 0:
            speedup = individual_time / gpu_time
            print(f"🚀 GPU speedup: {speedup:.2f}x")
        elif engine.gpu_available:
            print("✅ GPU evaluation completed (too fast to measure)")
        else:
            print("⚠️ GPU not available for batch evaluation")
        
        return True
        
    except Exception as e:
        print(f"❌ Batch fitness evaluation test failed: {e}")
        return False

def test_parallel_pruning_comparison():
    """Test parallel pruning comparison with ThreadPoolExecutor"""
    print("🧪 Testing Parallel Pruning Comparison...")
    
    try:
        from backend.simulator.pruning_engine import PruningEngine
        from backend.simulator.mutation_engine import MutationNode
        
        # Create test nodes
        test_nodes = []
        for i in range(50):
            node = MutationNode(
                id=f"test_node_{i}",
                sequence="MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
                parent_id="root",
                mutations=[],
                fitness=np.random.uniform(0.1, 1.0),
                generation=1,
                timestamp=datetime.now()
            )
            test_nodes.append(node)
        
        # Initialize pruning engine
        pruning_engine = PruningEngine(use_gpu=True, max_workers=4)
        
        # Define strategies to compare
        strategies = [
            {"method": "top_k", "params": {"k": 15}},
            {"method": "threshold", "params": {"threshold": 0.6}},
            {"method": "diversity", "params": {"target_size": 20}},
            {"method": "adaptive", "params": {"generation": 5, "max_generations": 10}}
        ]
        
        # Test parallel comparison
        start_time = time.time()
        parallel_results = pruning_engine.compare_pruning_strategies_parallel(test_nodes, strategies)
        parallel_time = time.time() - start_time
        
        print(f"✅ Parallel pruning comparison: {parallel_time:.4f}s")
        print(f"✅ Strategies compared: {len(parallel_results['results'])}")
        print(f"✅ Successful strategies: {parallel_results['comparison_metrics']['successful_strategies']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Parallel pruning comparison test failed: {e}")
        return False

def test_asynchronous_visualization():
    """Test asynchronous visualization rendering"""
    print("🧪 Testing Asynchronous Visualization...")
    
    try:
        from frontend.advanced_visualizations import render_visualization_with_spinner, get_async_viz_manager
        
        # Create test data
        test_data = {
            'tree': {
                'node1': {'sequence': 'MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG', 'fitness': 0.8},
                'node2': {'sequence': 'MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG', 'fitness': 0.9},
                'node3': {'sequence': 'MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG', 'fitness': 0.7}
            },
            'method': 'test',
            'metrics': {'nodes_before': 10, 'nodes_after': 3, 'pruning_ratio': 0.7}
        }
        
        # Test async visualization manager
        viz_manager = get_async_viz_manager()
        print(f"✅ Async visualization manager initialized with {viz_manager.max_workers} workers")
        
        # Test cached visualization generation
        start_time = time.time()
        fig = render_visualization_with_spinner(
            viz_type="mutation_tree",
            data=test_data,
            title="Test Mutation Tree"
        )
        viz_time = time.time() - start_time
        
        print(f"✅ Visualization generated: {viz_time:.4f}s")
        print(f"✅ Figure type: {type(fig)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Asynchronous visualization test failed: {e}")
        return False

def test_lightweight_serialization():
    """Test lightweight serialization formats"""
    print("🧪 Testing Lightweight Serialization...")
    
    try:
        from backend.utils.data_serialization import save_data_optimized, load_data_optimized, serializer
        
        # Create test data
        test_tree_data = {
            'node1': {'sequence': 'MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG', 'fitness': 0.8, 'generation': 1, 'mutations': []},
            'node2': {'sequence': 'MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG', 'fitness': 0.9, 'generation': 1, 'mutations': []},
            'node3': {'sequence': 'MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG', 'fitness': 0.7, 'generation': 1, 'mutations': []}
        }
        
        test_sequences = [
            'MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG',
            'MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG',
            'MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG'
        ]
        
        # Test tree serialization
        start_time = time.time()
        tree_filename = save_data_optimized(test_tree_data, "test_tree.npz", "tree")
        tree_save_time = time.time() - start_time
        
        start_time = time.time()
        loaded_tree = load_data_optimized(tree_filename, "tree")
        tree_load_time = time.time() - start_time
        
        print(f"✅ Tree save: {tree_save_time:.4f}s")
        print(f"✅ Tree load: {tree_load_time:.4f}s")
        print(f"✅ Tree data integrity: {len(loaded_tree) == len(test_tree_data)}")
        
        # Test sequences serialization
        start_time = time.time()
        seq_filename = save_data_optimized(test_sequences, "test_sequences.pkl.gz", "sequences")
        seq_save_time = time.time() - start_time
        
        start_time = time.time()
        loaded_sequences = load_data_optimized(seq_filename, "sequences")
        seq_load_time = time.time() - start_time
        
        print(f"✅ Sequences save: {seq_save_time:.4f}s")
        print(f"✅ Sequences load: {seq_load_time:.4f}s")
        print(f"✅ Sequences data integrity: {len(loaded_sequences) == len(test_sequences)}")
        
        # Clean up test files
        for filename in [tree_filename, seq_filename]:
            if filename and os.path.exists(filename):
                os.remove(filename)
                print(f"🗑️ Cleaned up {filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ Lightweight serialization test failed: {e}")
        return False

def test_gpu_optimization():
    """Test GPU optimization features"""
    print("🧪 Testing GPU Optimization...")
    
    try:
        from backend.utils.gpu_utils import get_device_info, is_gpu_available, get_dynamic_memory_status
        
        # Test GPU availability
        gpu_available = is_gpu_available()
        print(f"✅ GPU available: {gpu_available}")
        
        if gpu_available:
            # Test device info
            device_info = get_device_info()
            print(f"✅ Device: {device_info}")
            
            # Test dynamic memory monitoring
            memory_status = get_dynamic_memory_status()
            print(f"✅ Memory status: {memory_status['current']['allocated_gb']:.1f}GB / {memory_status['current']['total_gb']:.1f}GB")
            
            # Test cuDNN benchmark
            try:
                import torch
                torch.backends.cudnn.benchmark = True
                print("✅ cuDNN benchmark enabled")
            except ImportError:
                print("⚠️ PyTorch not available for cuDNN test")
        
        return True
        
    except Exception as e:
        print(f"❌ GPU optimization test failed: {e}")
        return False

def main():
    """Run all performance optimization tests"""
    print("🚀 Performance Optimizations Test Suite")
    print("=" * 50)
    
    tests = [
        ("GPU Optimization", test_gpu_optimization),
        ("Parallel Mutation Generation", test_parallel_mutation_generation),
        ("Batch Fitness Evaluation", test_batch_fitness_evaluation),
        ("Parallel Pruning Comparison", test_parallel_pruning_comparison),
        ("Asynchronous Visualization", test_asynchronous_visualization),
        ("Lightweight Serialization", test_lightweight_serialization)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            success = test_func()
            results[test_name] = success
            if success:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*50}")
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All performance optimizations working correctly!")
    else:
        print("⚠️ Some optimizations need attention")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 