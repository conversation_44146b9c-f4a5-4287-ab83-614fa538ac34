#!/usr/bin/env python3
"""
Test script for 3D protein visualization
"""
import sys
import os

# Add backend to path
sys.path.append('.')

def test_3d_visualization():
    """Test the 3D visualization components"""
    print("🧪 Testing 3D Protein Visualization Components")
    print("=" * 50)
    
    try:
        # Test imports
        print("1. Testing imports...")
        import py3Dmol
        print("   ✅ py3Dmol imported successfully")
        
        import stmol
        print("   ✅ stmol imported successfully")
        
        from backend.analyzer.protein_3d import Protein3DVisualizer, create_simple_protein_view
        print("   ✅ Protein3DVisualizer imported successfully")
        
        # Test basic functionality
        print("\n2. Testing basic functionality...")
        test_sequence = "MFVFLVLLPLVSSQCVNLTTRTQLPPAYTNSFTRGVYYPDKVFRSSVLH"
        
        visualizer = Protein3DVisualizer()
        print("   ✅ Protein3DVisualizer created successfully")
        
        # Test mock PDB creation
        mock_pdb = visualizer.create_mock_pdb_structure(test_sequence[:20])
        print("   ✅ Mock PDB structure created successfully")
        
        # Test basic viewer
        viewer = visualizer.create_basic_viewer()
        print("   ✅ Basic viewer created successfully")
        
        # Test simple protein view
        simple_view = create_simple_protein_view(test_sequence[:20])
        print("   ✅ Simple protein view created successfully")
        
        # Test mutation comparison
        mutations = [(5, 'L', 'A'), (10, 'T', 'S'), (15, 'G', 'V')]
        mutated_seq = list(test_sequence[:20])
        for pos, from_aa, to_aa in mutations:
            mutated_seq[pos] = to_aa
        mutated_sequence = ''.join(mutated_seq)
        
        comparison_view = visualizer.create_mutation_comparison_view(
            test_sequence[:20], mutated_sequence, mutations
        )
        print("   ✅ Mutation comparison view created successfully")
        
        # Test conservation view
        conservation_scores = [0.9, 0.8, 0.7, 0.6, 0.5] * 4  # 20 scores
        conservation_view = visualizer.create_conservation_view(test_sequence[:20], conservation_scores)
        print("   ✅ Conservation view created successfully")
        
        # Test binding sites view
        binding_sites = [(2, 8), (12, 18)]
        binding_view = visualizer.create_binding_site_view(test_sequence[:20], binding_sites)
        print("   ✅ Binding sites view created successfully")
        
        # Test interactive explorer
        mock_mutations = [
            {'position': i, 'from_aa': test_sequence[i], 'to_aa': 'A', 'impact': 'medium'}
            for i in range(0, 20, 5)
        ]
        explorer_view = visualizer.create_interactive_mutation_explorer(test_sequence[:20], mock_mutations)
        print("   ✅ Interactive explorer created successfully")
        
        print("\n🎉 All 3D visualization tests passed!")
        print("✅ 3D visualization is working correctly")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Please install required packages:")
        print("   pip install py3Dmol>=2.0.0 stmol>=0.0.9")
        return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_streamlit_integration():
    """Test Streamlit integration"""
    print("\n🌐 Testing Streamlit Integration")
    print("=" * 30)
    
    try:
        import streamlit as st
        print("   ✅ Streamlit imported successfully")
        
        # Test if the display function can be imported
        from frontend.streamlit_app import display_3d_visualization
        print("   ✅ display_3d_visualization function imported successfully")
        
        print("✅ Streamlit integration is ready")
        return True
        
    except ImportError as e:
        print(f"❌ Streamlit import error: {e}")
        return False
        
    except Exception as e:
        print(f"❌ Streamlit integration test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧬 Virus Mutation Simulation - 3D Visualization Test")
    print("=" * 60)
    
    # Run tests
    viz_test = test_3d_visualization()
    streamlit_test = test_streamlit_integration()
    
    print("\n📊 Test Summary")
    print("=" * 20)
    print(f"3D Visualization: {'✅ PASS' if viz_test else '❌ FAIL'}")
    print(f"Streamlit Integration: {'✅ PASS' if streamlit_test else '❌ FAIL'}")
    
    if viz_test and streamlit_test:
        print("\n🎉 All tests passed! 3D visualization is ready to use.")
        print("\n🚀 To run the application:")
        print("   streamlit run frontend/streamlit_app.py")
    else:
        print("\n⚠️  Some tests failed. Please check the error messages above.")
        
    print("\n" + "=" * 60)