
# Add this to your Streamlit app for better 3D visualization

def create_robust_3d_view(sequence, width=800, height=400):
    """Create a robust 3D visualization that avoids white block issues"""
    import py3Dmol
    
    # Create PDB with guaranteed valid format
    pdb_lines = ["HEADER    ROBUST PROTEIN STRUCTURE"]
    
    for i, aa in enumerate(sequence[:50]):  # Limit to 50 residues
        # Use simple linear arrangement with proper spacing
        x = i * 3.8  # Standard CA-CA distance
        y = 0.0
        z = 0.0
        
        # Ensure proper PDB format with exact spacing
        pdb_line = f"ATOM  {i+1:5d}  CA  {aa:3s} A{i+1:4d}    {x:8.3f}{y:8.3f}{z:8.3f}  1.00 20.00           C"
        pdb_lines.append(pdb_line)
    
    pdb_lines.append("END")
    pdb_data = '\n'.join(pdb_lines)
    
    # Create viewer with explicit settings
    viewer = py3Dmol.view(width=width, height=height)
    
    # Set non-white background to detect rendering issues
    viewer.setBackgroundColor('#f8f9fa')
    
    # Add model with error checking
    try:
        viewer.addModel(pdb_data, 'pdb')
        
        # Apply multiple styles for visibility
        viewer.setStyle({}, {'cartoon': {'color': 'spectrum', 'opacity': 0.8}})
        viewer.addStyle({}, {'stick': {'radius': 0.1, 'color': 'gray'}})
        
        # Ensure proper view
        viewer.center()
        viewer.zoomTo()
        
        return viewer
        
    except Exception as e:
        st.error(f"3D visualization error: {e}")
        return None

# Usage in Streamlit:
# viewer = create_robust_3d_view(sequence)
# if viewer:
#     showmol(viewer, height=400, width=800)
# else:
#     st.error("3D visualization failed")
    