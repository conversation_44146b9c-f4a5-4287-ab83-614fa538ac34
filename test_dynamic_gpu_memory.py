#!/usr/bin/env python3
"""
Test script to verify dynamic GPU memory monitoring
"""
import time
import sys
import os

# Add paths
sys.path.append('.')

def test_dynamic_memory_monitoring():
    """Test the dynamic GPU memory monitoring functionality"""
    print("🔍 Testing Dynamic GPU Memory Monitoring")
    print("=" * 50)
    
    try:
        from backend.utils.gpu_utils import (
            get_dynamic_memory_status, 
            get_gpu_manager, 
            is_gpu_available,
            monitor_memory_usage,
            get_memory_trend
        )
        
        print("✅ GPU utilities imported successfully")
        
        # Check GPU availability
        gpu_available = is_gpu_available()
        print(f"🎮 GPU Available: {gpu_available}")
        
        if gpu_available:
            # Get initial memory status
            print("\n📊 Initial Memory Status:")
            memory_status = get_dynamic_memory_status()
            memory_info = memory_status['current']
            memory_trend = memory_status['trend']
            
            print(f"   Allocated: {memory_info['allocated_gb']:.3f}GB")
            print(f"   Total: {memory_info['total_gb']:.3f}GB")
            print(f"   Free: {memory_info['free_gb']:.3f}GB")
            print(f"   Trend: {memory_trend['trend']} (rate: {memory_trend['change_rate']:.3f})")
            
            # Test memory monitoring over time
            print("\n⏱️  Monitoring memory for 5 seconds...")
            start_time = time.time()
            
            for i in range(5):
                memory_status = get_dynamic_memory_status()
                memory_info = memory_status['current']
                memory_trend = memory_status['trend']
                
                print(f"   {i+1}s: {memory_info['allocated_gb']:.3f}GB | Trend: {memory_trend['trend']}")
                time.sleep(1)
            
            print(f"\n⏱️  Monitoring completed in {time.time() - start_time:.1f}s")
            
            # Test GPU acceleration
            print("\n🚀 Testing GPU Acceleration:")
            
            try:
                import torch
                import numpy as np
                
                # Create some test tensors
                device = get_gpu_manager().get_device()
                print(f"   Using device: {device}")
                
                # Test tensor operations
                test_tensor = torch.randn(1000, 1000, device=device)
                result = torch.matmul(test_tensor, test_tensor.T)
                
                # Check memory after operation
                memory_after = monitor_memory_usage()
                print(f"   Memory after operation: {memory_after['allocated_gb']:.3f}GB")
                
                # Clear cache
                torch.cuda.empty_cache()
                memory_cleared = monitor_memory_usage()
                print(f"   Memory after clearing: {memory_cleared['allocated_gb']:.3f}GB")
                
                print("   ✅ GPU acceleration test passed")
                
            except Exception as e:
                print(f"   ❌ GPU acceleration test failed: {e}")
            
            # Test memory trend analysis
            print("\n📈 Testing Memory Trend Analysis:")
            trend = get_memory_trend()
            print(f"   Current trend: {trend['trend']}")
            print(f"   Change rate: {trend['change_rate']:.3f}")
            print(f"   Current value: {trend['current']:.3f}")
            print(f"   Average: {trend['average']:.3f}")
            
        else:
            print("💻 GPU not available, testing CPU fallback")
            
            # Test CPU memory monitoring
            memory_info = monitor_memory_usage()
            print(f"   CPU Memory: {memory_info['allocated_gb']:.3f}GB / {memory_info['total_gb']:.3f}GB")
        
        print("\n✅ Dynamic GPU memory monitoring test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gpu_accelerated_computations():
    """Test the GPU accelerated computations wrapper"""
    print("\n🚀 Testing GPU Accelerated Computations")
    print("=" * 50)
    
    try:
        from backend.utils.gpu_accelerated_computations import (
            get_gpu_accelerated,
            accelerate_matrix_multiplication,
            accelerate_data_processing_pipeline,
            get_computation_performance_metrics
        )
        
        print("✅ GPU accelerated computations imported successfully")
        
        gpu_acc = get_gpu_accelerated()
        
        # Test matrix operations
        print("\n🧮 Testing Matrix Operations:")
        import numpy as np
        
        matrices = [np.random.rand(100, 100) for _ in range(2)]
        result = accelerate_matrix_multiplication(matrices)
        print(f"   Matrix multiplication result shape: {result.shape}")
        
        # Test data processing
        print("\n📊 Testing Data Processing:")
        data = np.random.rand(500, 500)
        operations = ['normalize', 'scale']
        result = accelerate_data_processing_pipeline(data, operations)
        print(f"   Data processing result shape: {result.shape}")
        
        # Get performance metrics
        print("\n📈 Performance Metrics:")
        metrics = get_computation_performance_metrics()
        print(f"   GPU Available: {metrics['gpu_available']}")
        
        if 'memory_status' in metrics and metrics['memory_status']:
            memory = metrics['memory_status']['current']
            print(f"   Memory Usage: {memory['allocated_gb']:.3f}GB / {memory['total_gb']:.3f}GB")
        
        print("✅ GPU accelerated computations test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ GPU accelerated computations test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧬 Dynamic GPU Memory Monitoring Test")
    print("=" * 60)
    
    # Run tests
    memory_test = test_dynamic_memory_monitoring()
    computation_test = test_gpu_accelerated_computations()
    
    print("\n📊 Test Summary")
    print("=" * 20)
    print(f"Memory Monitoring: {'✅ PASS' if memory_test else '❌ FAIL'}")
    print(f"GPU Computations: {'✅ PASS' if computation_test else '❌ FAIL'}")
    
    if memory_test and computation_test:
        print("\n🎉 All GPU tests passed!")
        print("\n💡 Features verified:")
        print("   • Dynamic GPU memory monitoring")
        print("   • Real-time memory trend analysis")
        print("   • Automatic GPU/CPU fallback")
        print("   • GPU-accelerated computations")
        print("   • Memory-efficient operations")
        
        print("\n🚀 The application now uses GPU acceleration with intelligent fallback!")
    else:
        print("\n⚠️  Some tests failed. Check the error messages above.")
        
    print("\n" + "=" * 60) 