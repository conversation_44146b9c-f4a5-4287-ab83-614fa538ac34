#!/usr/bin/env python3
"""
Fix for 3D visualization white block issue
"""
import sys
import os

# Add paths
sys.path.append('.')

def create_working_3d_example():
    """Create a working 3D visualization example"""
    print("🔧 Creating Working 3D Visualization Example")
    print("=" * 45)
    
    try:
        import py3Dmol
        
        # Create a very simple, guaranteed-to-work structure
        simple_pdb = """HEADER    WORKING PROTEIN STRUCTURE
REMARK   This structure should definitely render
ATOM      1  CA  ALA A   1       0.000   0.000   0.000  1.00 20.00           C
ATOM      2  CA  GLY A   2       3.800   0.000   0.000  1.00 20.00           C
ATOM      3  CA  VAL A   3       7.600   0.000   0.000  1.00 20.00           C
ATOM      4  CA  LEU A   4      11.400   0.000   0.000  1.00 20.00           C
ATOM      5  CA  PHE A   5      15.200   0.000   0.000  1.00 20.00           C
ATOM      6  CA  LEU A   6      19.000   0.000   0.000  1.00 20.00           C
ATOM      7  CA  VAL A   7      22.800   0.000   0.000  1.00 20.00           C
ATOM      8  CA  LEU A   8      26.600   0.000   0.000  1.00 20.00           C
ATOM      9  CA  LEU A   9      30.400   0.000   0.000  1.00 20.00           C
ATOM     10  CA  PRO A  10      34.200   0.000   0.000  1.00 20.00           C
END"""
        
        print("   ✅ Simple PDB structure created")
        
        # Create viewer with explicit settings to prevent white block
        viewer = py3Dmol.view(width=800, height=500)
        
        # Set background color (not white to see if it's rendering)
        viewer.setBackgroundColor('#f0f0f0')
        
        # Add model
        viewer.addModel(simple_pdb, 'pdb')
        print("   ✅ Model added to viewer")
        
        # Set multiple styles to ensure visibility
        viewer.setStyle({}, {'cartoon': {'color': 'red', 'opacity': 0.8}})
        viewer.addStyle({}, {'stick': {'color': 'blue', 'radius': 0.2}})
        
        print("   ✅ Styles applied (cartoon + stick)")
        
        # Ensure proper centering and zooming
        viewer.center()
        viewer.zoomTo()
        
        # Add some labels for debugging
        viewer.addLabel("N-terminus", {'position': {'x': 0, 'y': 0, 'z': 5}})
        viewer.addLabel("C-terminus", {'position': {'x': 34, 'y': 0, 'z': 5}})
        
        print("   ✅ Labels added for debugging")
        
        # Create comprehensive HTML with debugging
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>3D Visualization Fix Test</title>
    <script src="https://3Dmol.csb.pitt.edu/build/3Dmol-min.js"></script>
    <style>
        body {{ 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #fafafa;
        }}
        #viewer {{ 
            border: 3px solid #333; 
            margin: 20px 0; 
            background-color: white;
        }}
        .status {{ 
            background: #d4edda; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }}
        .debug {{ 
            background: #fff3cd; 
            padding: 10px; 
            border-radius: 5px; 
            margin: 10px 0;
            border: 1px solid #ffeaa7;
            font-family: monospace;
            font-size: 12px;
        }}
        .controls {{
            margin: 20px 0;
            padding: 15px;
            background: #e9ecef;
            border-radius: 5px;
        }}
        button {{
            margin: 5px;
            padding: 8px 15px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }}
        button:hover {{ background: #0056b3; }}
    </style>
</head>
<body>
    <h1>🔧 3D Visualization Fix Test</h1>
    
    <div class="status">
        <h3>✅ Status: Working 3D Visualization</h3>
        <p><strong>Sequence:</strong> AGVLFLVLLP (10 residues)</p>
        <p><strong>Structure:</strong> Linear arrangement with 3.8Å spacing</p>
        <p><strong>Styles:</strong> Red cartoon + Blue sticks</p>
        <p><strong>Background:</strong> Light gray (#f0f0f0)</p>
    </div>
    
    <div id="viewer" style="width: 800px; height: 500px;"></div>
    
    <div class="controls">
        <h3>🎮 Controls:</h3>
        <button onclick="viewer.setStyle({{}}, {{'cartoon': {{'color': 'red'}}}});">Red Cartoon</button>
        <button onclick="viewer.setStyle({{}}, {{'stick': {{'color': 'blue', 'radius': 0.3}}}});">Blue Sticks</button>
        <button onclick="viewer.setStyle({{}}, {{'sphere': {{'color': 'green', 'radius': 0.8}}}});">Green Spheres</button>
        <button onclick="viewer.zoomTo();">Reset View</button>
        <button onclick="viewer.setBackgroundColor('white');">White Background</button>
        <button onclick="viewer.setBackgroundColor('#f0f0f0');">Gray Background</button>
    </div>
    
    <div class="debug">
        <h3>🔍 Debug Information:</h3>
        <p>If you see a protein structure above, the 3D visualization is working correctly.</p>
        <p>If you see only a white/gray block, there may be an issue with:</p>
        <ul>
            <li>Browser WebGL support</li>
            <li>3Dmol.js library loading</li>
            <li>PDB data format</li>
            <li>Viewer initialization</li>
        </ul>
        <p><strong>Browser Console:</strong> Press F12 and check for JavaScript errors</p>
    </div>
    
    <script>
        // Initialize viewer
        var viewer;
        
        // Add error handling
        try {{
            {viewer.js()}
            console.log("✅ 3Dmol viewer initialized successfully");
        }} catch (error) {{
            console.error("❌ 3Dmol viewer initialization failed:", error);
            document.getElementById('viewer').innerHTML = 
                '<div style="padding: 50px; text-align: center; color: red;">' +
                '❌ 3D Visualization Failed<br>' +
                'Error: ' + error.message + 
                '</div>';
        }}
        
        // Test WebGL support
        function testWebGL() {{
            try {{
                var canvas = document.createElement('canvas');
                var gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                return gl && gl instanceof WebGLRenderingContext;
            }} catch (e) {{
                return false;
            }}
        }}
        
        if (!testWebGL()) {{
            console.warn("⚠️ WebGL not supported - 3D visualization may not work");
        }} else {{
            console.log("✅ WebGL is supported");
        }}
    </script>
</body>
</html>
        """
        
        with open('3d_fix_test.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print("   📁 Fix test HTML saved: 3d_fix_test.html")
        print("   🌐 Open this file in your browser")
        print("   💡 If you see a protein structure, 3D visualization works")
        print("   🔍 If you see a white block, check browser console (F12)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Failed to create working example: {e}")
        return False

def diagnose_white_block_issue():
    """Diagnose common causes of white block issue"""
    print("\n🔍 Diagnosing White Block Issue")
    print("=" * 35)
    
    # Common causes and solutions
    issues = [
        {
            'issue': 'PDB format errors',
            'solution': 'Ensure proper ATOM record formatting',
            'test': 'Check if ATOM lines have correct spacing and format'
        },
        {
            'issue': 'Empty or invalid structure',
            'solution': 'Verify PDB contains valid coordinates',
            'test': 'Count ATOM records and check coordinate values'
        },
        {
            'issue': 'Viewer not properly initialized',
            'solution': 'Call zoomTo() and center() after adding model',
            'test': 'Ensure viewer.js() generates valid JavaScript'
        },
        {
            'issue': 'Style not applied correctly',
            'solution': 'Use explicit style dictionaries',
            'test': 'Try different visualization styles (cartoon, stick, sphere)'
        },
        {
            'issue': 'WebGL not supported',
            'solution': 'Check browser WebGL support',
            'test': 'Test WebGL in browser console'
        },
        {
            'issue': 'JavaScript errors',
            'solution': 'Check browser console for errors',
            'test': 'Open F12 developer tools and check console'
        }
    ]
    
    print("   🔧 Common causes and solutions:")
    for i, item in enumerate(issues, 1):
        print(f"   {i}. {item['issue']}")
        print(f"      Solution: {item['solution']}")
        print(f"      Test: {item['test']}")
        print()
    
    return True

def create_streamlit_fix():
    """Create a fix for Streamlit 3D visualization"""
    print("🌐 Creating Streamlit Fix")
    print("=" * 25)
    
    fix_code = '''
# Add this to your Streamlit app for better 3D visualization

def create_robust_3d_view(sequence, width=800, height=400):
    """Create a robust 3D visualization that avoids white block issues"""
    import py3Dmol
    
    # Create PDB with guaranteed valid format
    pdb_lines = ["HEADER    ROBUST PROTEIN STRUCTURE"]
    
    for i, aa in enumerate(sequence[:50]):  # Limit to 50 residues
        # Use simple linear arrangement with proper spacing
        x = i * 3.8  # Standard CA-CA distance
        y = 0.0
        z = 0.0
        
        # Ensure proper PDB format with exact spacing
        pdb_line = f"ATOM  {i+1:5d}  CA  {aa:3s} A{i+1:4d}    {x:8.3f}{y:8.3f}{z:8.3f}  1.00 20.00           C"
        pdb_lines.append(pdb_line)
    
    pdb_lines.append("END")
    pdb_data = '\\n'.join(pdb_lines)
    
    # Create viewer with explicit settings
    viewer = py3Dmol.view(width=width, height=height)
    
    # Set non-white background to detect rendering issues
    viewer.setBackgroundColor('#f8f9fa')
    
    # Add model with error checking
    try:
        viewer.addModel(pdb_data, 'pdb')
        
        # Apply multiple styles for visibility
        viewer.setStyle({}, {'cartoon': {'color': 'spectrum', 'opacity': 0.8}})
        viewer.addStyle({}, {'stick': {'radius': 0.1, 'color': 'gray'}})
        
        # Ensure proper view
        viewer.center()
        viewer.zoomTo()
        
        return viewer
        
    except Exception as e:
        st.error(f"3D visualization error: {e}")
        return None

# Usage in Streamlit:
# viewer = create_robust_3d_view(sequence)
# if viewer:
#     showmol(viewer, height=400, width=800)
# else:
#     st.error("3D visualization failed")
    '''
    
    with open('streamlit_3d_fix.py', 'w', encoding='utf-8') as f:
        f.write(fix_code)
    
    print("   📁 Streamlit fix code saved: streamlit_3d_fix.py")
    print("   💡 Copy the create_robust_3d_view function to your Streamlit app")
    
    return True

if __name__ == "__main__":
    print("🔧 3D Visualization White Block Fix")
    print("=" * 40)
    
    # Run diagnostics and create fixes
    example_test = create_working_3d_example()
    diagnosis = diagnose_white_block_issue()
    streamlit_fix = create_streamlit_fix()
    
    print("\n📊 Fix Summary")
    print("=" * 15)
    print(f"Working Example: {'✅ CREATED' if example_test else '❌ FAILED'}")
    print(f"Issue Diagnosis: {'✅ COMPLETED' if diagnosis else '❌ FAILED'}")
    print(f"Streamlit Fix: {'✅ CREATED' if streamlit_fix else '❌ FAILED'}")
    
    if example_test:
        print("\n🎯 Next Steps:")
        print("1. Open '3d_fix_test.html' in your browser")
        print("2. If you see a protein structure → 3D visualization works")
        print("3. If you see a white block → check browser console (F12)")
        print("4. Apply the Streamlit fix from 'streamlit_3d_fix.py'")
        
        print("\n💡 Key fixes applied:")
        print("   • Fixed PDB format with proper spacing")
        print("   • Added explicit viewer initialization")
        print("   • Used non-white background for debugging")
        print("   • Added multiple visualization styles")
        print("   • Included comprehensive error handling")
        
    print("\n" + "=" * 40)