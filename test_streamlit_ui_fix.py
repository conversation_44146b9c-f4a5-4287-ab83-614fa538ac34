#!/usr/bin/env python3
"""
Test script to verify Streamlit UI duplicate key fix
"""
import sys
import os

# Add frontend to path
sys.path.append('.')

def test_streamlit_compilation():
    """Test that Streamlit app compiles without errors"""
    print("🧪 Testing Streamlit App Compilation")
    print("=" * 40)
    
    try:
        # Test Python compilation
        import py_compile
        py_compile.compile('frontend/streamlit_app.py', doraise=True)
        print("   ✅ Python compilation successful")
        
        # Test imports
        import streamlit as st
        print("   ✅ Streamlit import successful")
        
        # Test if we can import the main functions
        sys.path.append('frontend')
        from streamlit_app import display_3d_visualization, display_structural_analysis
        print("   ✅ Function imports successful")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Compilation failed: {e}")
        return False

def test_unique_keys():
    """Test that all UI elements have unique keys"""
    print("\n🔍 Testing Unique Keys")
    print("=" * 25)
    
    try:
        # Read the streamlit app file
        with open('frontend/streamlit_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find all key parameters
        import re
        key_pattern = r'key=[\'"](.*?)[\'"]'
        keys = re.findall(key_pattern, content)
        
        print(f"   Found {len(keys)} explicit keys")
        
        # Check for duplicates
        unique_keys = set(keys)
        if len(keys) == len(unique_keys):
            print("   ✅ All keys are unique")
            
            # Show the keys we added
            relevant_keys = [k for k in keys if any(x in k for x in ['viz_type', 'structure_type', 'max_residues', 'color_scheme'])]
            if relevant_keys:
                print("   🔧 Fixed duplicate keys:")
                for key in relevant_keys:
                    print(f"      • {key}")
            
            return True
        else:
            duplicates = [k for k in keys if keys.count(k) > 1]
            print(f"   ❌ Found duplicate keys: {set(duplicates)}")
            return False
            
    except Exception as e:
        print(f"   ❌ Key analysis failed: {e}")
        return False

def test_selectbox_patterns():
    """Test for potential selectbox conflicts"""
    print("\n📋 Testing Selectbox Patterns")
    print("=" * 30)
    
    try:
        with open('frontend/streamlit_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find all selectbox calls
        import re
        selectbox_pattern = r'st\.selectbox\(\s*[\'"]([^\'\"]*)[\'"]'
        selectbox_labels = re.findall(selectbox_pattern, content)
        
        print(f"   Found {len(selectbox_labels)} selectbox elements")
        
        # Check for potential conflicts
        label_counts = {}
        for label in selectbox_labels:
            label_counts[label] = label_counts.get(label, 0) + 1
        
        conflicts = {label: count for label, count in label_counts.items() if count > 1}
        
        if conflicts:
            print("   ⚠️  Potential conflicts found:")
            for label, count in conflicts.items():
                print(f"      • '{label}': {count} instances")
            
            # Check if they have unique keys
            key_pattern = r'st\.selectbox\(\s*[\'"]' + re.escape(label) + r'[\'"].*?key=[\'"](.*?)[\'"]'
            for label in conflicts.keys():
                keys_for_label = re.findall(key_pattern, content, re.DOTALL)
                if len(keys_for_label) == conflicts[label]:
                    print(f"      ✅ '{label}' has unique keys: {keys_for_label}")
                else:
                    print(f"      ❌ '{label}' missing unique keys")
        else:
            print("   ✅ No selectbox label conflicts")
        
        return len(conflicts) == 0 or all(
            len(re.findall(r'st\.selectbox\(\s*[\'"]' + re.escape(label) + r'[\'"].*?key=', content, re.DOTALL)) == count
            for label, count in conflicts.items()
        )
        
    except Exception as e:
        print(f"   ❌ Selectbox analysis failed: {e}")
        return False

if __name__ == "__main__":
    print("🧬 Virus Mutation Simulation - Streamlit UI Fix Test")
    print("=" * 55)
    
    # Run tests
    compilation_test = test_streamlit_compilation()
    keys_test = test_unique_keys()
    selectbox_test = test_selectbox_patterns()
    
    print("\n📊 Test Summary")
    print("=" * 20)
    print(f"Streamlit Compilation: {'✅ PASS' if compilation_test else '❌ FAIL'}")
    print(f"Unique Keys: {'✅ PASS' if keys_test else '❌ FAIL'}")
    print(f"Selectbox Patterns: {'✅ PASS' if selectbox_test else '❌ FAIL'}")
    
    if compilation_test and keys_test and selectbox_test:
        print("\n🎉 All UI tests passed!")
        print("\n💡 Fixes applied:")
        print("   • Added unique keys to all duplicate selectbox elements")
        print("   • Fixed 'Visualization Type' conflicts between tabs")
        print("   • Fixed 'Structure Type' conflicts between tabs")
        print("   • Fixed 'Max Residues' slider conflicts")
        print("   • Added 'Color Scheme' unique key")
        
        print("\n🚀 The 3D visualization should now work without ID conflicts!")
    else:
        print("\n⚠️  Some tests failed. Please check the error messages above.")
        
    print("\n" + "=" * 55)