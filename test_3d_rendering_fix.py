#!/usr/bin/env python3
"""
Test script to diagnose and fix 3D visualization rendering issues
"""
import sys
import os

# Add backend to path
sys.path.append('.')

def test_pdb_format():
    """Test PDB format generation"""
    print("🧪 Testing PDB Format Generation")
    print("=" * 35)
    
    try:
        from backend.analyzer.protein_3d import Protein3DVisualizer
        
        visualizer = Protein3DVisualizer()
        test_sequence = "MFVFLVLLPLVSSQCVNLTTRTQLPPAYTNSFTRGVYYPDKVFRSSVLH"[:10]  # Short sequence
        
        # Test PDB generation
        pdb_data = visualizer.create_mock_pdb_structure(test_sequence, 'helix')
        
        print(f"   ✅ PDB data generated ({len(pdb_data)} characters)")
        print("   📄 Sample PDB content:")
        
        lines = pdb_data.split('\n')
        for i, line in enumerate(lines[:5]):  # Show first 5 lines
            print(f"      {i+1}: {line}")
        
        if len(lines) > 5:
            print(f"      ... ({len(lines)-5} more lines)")
        
        # Validate PDB format
        atom_lines = [line for line in lines if line.startswith('ATOM')]
        print(f"   📊 Found {len(atom_lines)} ATOM records")
        
        if atom_lines:
            print("   ✅ PDB format appears valid")
            return True, pdb_data
        else:
            print("   ❌ No ATOM records found in PDB")
            return False, pdb_data
            
    except Exception as e:
        print(f"   ❌ PDB generation failed: {e}")
        return False, None

def test_py3dmol_viewer():
    """Test py3Dmol viewer creation and rendering"""
    print("\n🔬 Testing py3Dmol Viewer")
    print("=" * 30)
    
    try:
        import py3Dmol
        from backend.analyzer.protein_3d import Protein3DVisualizer
        
        visualizer = Protein3DVisualizer()
        
        # Test viewer creation
        viewer = visualizer.create_basic_viewer(400, 300)
        print("   ✅ Basic viewer created")
        
        # Test with simple PDB data
        test_sequence = "MFVFLVLLPLVSSQCVNLTTRTQLPPAYTNSFTRGVYYPDKVFRSSVLH"[:10]
        pdb_data = visualizer.create_mock_pdb_structure(test_sequence, 'helix')
        
        # Add structure to viewer
        viewer.addModel(pdb_data, 'pdb')
        print("   ✅ PDB model added to viewer")
        
        # Set style
        viewer.setStyle({'cartoon': {'color': 'spectrum'}})
        print("   ✅ Cartoon style applied")
        
        # Center and zoom
        viewer.zoomTo()
        viewer.center()
        print("   ✅ View centered and zoomed")
        
        # Test if viewer has content
        js_code = viewer.js()
        if js_code and len(js_code) > 100:
            print(f"   ✅ Viewer JavaScript generated ({len(js_code)} characters)")
            return True, viewer
        else:
            print("   ❌ Viewer JavaScript too short or empty")
            return False, viewer
            
    except Exception as e:
        print(f"   ❌ py3Dmol viewer test failed: {e}")
        return False, None

def test_simple_protein_view():
    """Test the simple protein view function"""
    print("\n🧬 Testing Simple Protein View")
    print("=" * 32)
    
    try:
        from backend.analyzer.protein_3d import create_simple_protein_view
        
        test_sequence = "MFVFLVLLPLVSSQCVNLTTRTQLPPAYTNSFTRGVYYPDKVFRSSVLH"[:15]
        
        # Test without mutations
        viewer = create_simple_protein_view(test_sequence, width=400, height=300)
        print("   ✅ Simple protein view created")
        
        # Test with mutations
        mutations = [(2, 'V', 'A'), (5, 'L', 'G'), (8, 'V', 'S')]
        viewer_with_mutations = create_simple_protein_view(test_sequence, mutations, width=400, height=300)
        print("   ✅ Protein view with mutations created")
        
        # Check if viewer has content
        js_code = viewer.js()
        if js_code and 'addModel' in js_code:
            print("   ✅ Viewer contains model data")
            return True, viewer
        else:
            print("   ❌ Viewer missing model data")
            return False, viewer
            
    except Exception as e:
        print(f"   ❌ Simple protein view test failed: {e}")
        return False, None

def test_streamlit_integration():
    """Test Streamlit integration"""
    print("\n🌐 Testing Streamlit Integration")
    print("=" * 33)
    
    try:
        import streamlit as st
        from stmol import showmol
        
        print("   ✅ Streamlit and stmol imported")
        
        # Test the fallback function
        from frontend.streamlit_app import create_mock_pdb_structure
        
        test_sequence = "MFVFLVLLPLVSSQCVNLTTRTQLPPAYTNSFTRGVYYPDKVFRSSVLH"[:10]
        fallback_pdb = create_mock_pdb_structure(test_sequence)
        
        print(f"   ✅ Fallback PDB created ({len(fallback_pdb)} characters)")
        
        # Test py3Dmol with fallback
        import py3Dmol
        
        viewer = py3Dmol.view(width=400, height=300)
        viewer.addModel(fallback_pdb, 'pdb')
        viewer.setStyle({'cartoon': {'color': 'spectrum'}})
        viewer.zoomTo()
        
        print("   ✅ Fallback viewer created successfully")
        
        return True, viewer
        
    except Exception as e:
        print(f"   ❌ Streamlit integration test failed: {e}")
        return False, None

def create_debug_visualization():
    """Create a debug visualization to identify rendering issues"""
    print("\n🔧 Creating Debug Visualization")
    print("=" * 35)
    
    try:
        import py3Dmol
        
        # Create a very simple test structure
        simple_pdb = """HEADER    DEBUG PROTEIN STRUCTURE
ATOM      1  CA  ALA A   1      -2.000   0.000   0.000  1.00 20.00           C
ATOM      2  CA  GLY A   2       0.000   0.000   0.000  1.00 20.00           C
ATOM      3  CA  VAL A   3       2.000   0.000   0.000  1.00 20.00           C
ATOM      4  CA  LEU A   4       4.000   0.000   0.000  1.00 20.00           C
ATOM      5  CA  PHE A   5       6.000   0.000   0.000  1.00 20.00           C
END"""
        
        print("   📄 Simple test PDB created")
        
        # Create viewer with explicit settings
        viewer = py3Dmol.view(width=600, height=400)
        viewer.setBackgroundColor('white')
        
        # Add model
        viewer.addModel(simple_pdb, 'pdb')
        
        # Try different styles
        styles_to_test = [
            {'cartoon': {'color': 'red'}},
            {'stick': {'color': 'blue', 'radius': 0.3}},
            {'sphere': {'color': 'green', 'radius': 0.8}},
            {'line': {'color': 'purple'}}
        ]
        
        for i, style in enumerate(styles_to_test):
            test_viewer = py3Dmol.view(width=300, height=200)
            test_viewer.setBackgroundColor('lightgray')
            test_viewer.addModel(simple_pdb, 'pdb')
            test_viewer.setStyle(style)
            test_viewer.zoomTo()
            
            style_name = list(style.keys())[0]
            print(f"   ✅ {style_name.title()} style viewer created")
        
        # Create final viewer with cartoon style
        final_viewer = py3Dmol.view(width=600, height=400)
        final_viewer.setBackgroundColor('white')
        final_viewer.addModel(simple_pdb, 'pdb')
        final_viewer.setStyle({'cartoon': {'color': 'spectrum'}})
        final_viewer.zoomTo()
        final_viewer.center()
        
        print("   ✅ Final debug viewer created")
        
        # Save as HTML for testing
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>3D Visualization Debug</title>
    <script src="https://3Dmol.csb.pitt.edu/build/3Dmol-min.js"></script>
</head>
<body>
    <h2>3D Visualization Debug Test</h2>
    <div id="viewer" style="width: 600px; height: 400px; border: 1px solid black;"></div>
    <script>
        {final_viewer.js()}
    </script>
</body>
</html>
        """
        
        with open('debug_3d_visualization.html', 'w') as f:
            f.write(html_content)
        
        print("   📁 Debug HTML file saved: debug_3d_visualization.html")
        
        return True, final_viewer
        
    except Exception as e:
        print(f"   ❌ Debug visualization failed: {e}")
        return False, None

if __name__ == "__main__":
    print("🧬 Virus Mutation Simulation - 3D Rendering Fix Test")
    print("=" * 55)
    
    # Run tests
    pdb_test, pdb_data = test_pdb_format()
    viewer_test, viewer = test_py3dmol_viewer()
    simple_test, simple_viewer = test_simple_protein_view()
    streamlit_test, streamlit_viewer = test_streamlit_integration()
    debug_test, debug_viewer = create_debug_visualization()
    
    print("\n📊 Test Summary")
    print("=" * 20)
    print(f"PDB Format Generation: {'✅ PASS' if pdb_test else '❌ FAIL'}")
    print(f"py3Dmol Viewer: {'✅ PASS' if viewer_test else '❌ FAIL'}")
    print(f"Simple Protein View: {'✅ PASS' if simple_test else '❌ FAIL'}")
    print(f"Streamlit Integration: {'✅ PASS' if streamlit_test else '❌ FAIL'}")
    print(f"Debug Visualization: {'✅ PASS' if debug_test else '❌ FAIL'}")
    
    if all([pdb_test, viewer_test, simple_test, streamlit_test, debug_test]):
        print("\n🎉 All 3D rendering tests passed!")
        print("\n💡 Fixes applied:")
        print("   • Fixed PDB format newline issue (\\\\n → \\n)")
        print("   • Added proper viewer initialization")
        print("   • Improved structure centering and zooming")
        print("   • Created debug visualization for testing")
        
        print("\n🚀 3D visualization should now render properly!")
        print("📁 Open 'debug_3d_visualization.html' in your browser to test")
    else:
        print("\n⚠️  Some tests failed. Check the error messages above.")
        
        if pdb_data:
            print("\n🔧 Debug Information:")
            print("PDB Data Sample:")
            print(pdb_data[:200] + "..." if len(pdb_data) > 200 else pdb_data)
        
    print("\n" + "=" * 55)