#!/usr/bin/env python3
"""
Test script to verify the mutation tree visualization fix
"""
import sys
import os

# Add paths
sys.path.append('.')

def test_mutation_tree_visualization():
    """Test the fixed mutation tree visualization"""
    print("🧪 Testing Mutation Tree Visualization Fix")
    print("=" * 45)
    
    try:
        from frontend.advanced_visualizations import AdvancedVisualizationSuite
        
        viz_suite = AdvancedVisualizationSuite()
        print("   ✅ AdvancedVisualizationSuite imported successfully")
        
        # Create test mutation tree data
        mutation_tree_data = {
            'nodes': [
                {'id': 'root', 'fitness': 1.0, 'generation': 0},
                {'id': 'node_1', 'fitness': 0.9, 'generation': 1},
                {'id': 'node_2', 'fitness': 0.8, 'generation': 1},
                {'id': 'node_3', 'fitness': 0.85, 'generation': 2},
                {'id': 'node_4', 'fitness': 0.75, 'generation': 2}
            ],
            'edges': [
                {'source': 'root', 'target': 'node_1'},
                {'source': 'root', 'target': 'node_2'},
                {'source': 'node_1', 'target': 'node_3'},
                {'source': 'node_2', 'target': 'node_4'}
            ]
        }
        
        print("   ✅ Test mutation tree data created")
        
        # Test without pruning info
        fig = viz_suite.create_interactive_mutation_tree(mutation_tree_data)
        print("   ✅ Mutation tree visualization created (no pruning)")
        
        # Test with pruning info
        pruning_info = {
            'pruned_nodes': [
                {'id': 'pruned_1', 'fitness': 0.3, 'generation': 2}
            ]
        }
        
        fig_with_pruning = viz_suite.create_interactive_mutation_tree(mutation_tree_data, pruning_info)
        print("   ✅ Mutation tree visualization created (with pruning)")
        
        # Test empty data
        empty_data = {'nodes': [], 'edges': []}
        fig_empty = viz_suite.create_interactive_mutation_tree(empty_data)
        print("   ✅ Empty mutation tree handled correctly")
        
        # Verify figure properties
        if hasattr(fig, 'data') and len(fig.data) > 0:
            print(f"   📊 Figure contains {len(fig.data)} traces")
            print("   ✅ Figure structure is valid")
        else:
            print("   ⚠️  Figure has no data traces")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_other_visualizations():
    """Test other advanced visualizations"""
    print("\n🔬 Testing Other Advanced Visualizations")
    print("=" * 42)
    
    try:
        from frontend.advanced_visualizations import AdvancedVisualizationSuite
        import numpy as np
        
        viz_suite = AdvancedVisualizationSuite()
        
        # Test AI explainability dashboard
        ai_data = {
            'confidence_scores': np.random.beta(2, 2, 100),
            'features': [f'Feature_{i}' for i in range(10)],
            'importance': np.random.exponential(1, 10),
            'attention_matrix': np.random.rand(10, 10),
            'predictions': np.random.rand(50),
            'uncertainties': np.random.rand(50) * 0.3
        }
        
        explainability_fig = viz_suite.create_ai_explainability_dashboard(ai_data)
        print("   ✅ AI explainability dashboard created")
        
        # Test comparative analysis dashboard
        comparison_data = {
            'performance': {
                'Method_A': np.random.uniform(0.7, 0.9, 4),
                'Method_B': np.random.uniform(0.75, 0.85, 4),
                'Method_C': np.random.uniform(0.8, 0.95, 4)
            },
            'complexity': {
                'Training Time': [1, 3, 5],
                'Inference Time': [0.1, 0.3, 0.5],
                'Memory Usage': [100, 300, 500]
            },
            'accuracy': np.random.uniform(0.7, 0.95, 3),
            'speed': np.random.uniform(0.1, 2.0, 3),
            'ablation': [0.89, 0.75, 0.78, 0.82, 0.65]
        }
        
        comparison_fig = viz_suite.create_comparative_analysis_dashboard(comparison_data)
        print("   ✅ Comparative analysis dashboard created")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Other visualizations test failed: {e}")
        return False

def test_research_visualizations():
    """Test research visualization suite"""
    print("\n📊 Testing Research Visualizations")
    print("=" * 35)
    
    try:
        from frontend.research_visualizations import ResearchVisualizationSuite
        import numpy as np
        
        research_viz = ResearchVisualizationSuite()
        print("   ✅ ResearchVisualizationSuite imported successfully")
        
        # Test mutation impact matrix
        mutation_data = {
            'mutations': [f'Mut_{i}' for i in range(8)],
            'properties': ['Transmissibility', 'Virulence', 'Stability', 'Binding'],
            'impact_matrix': np.random.uniform(-2, 2, (8, 4))
        }
        
        impact_fig = research_viz.create_mutation_impact_matrix(mutation_data)
        print("   ✅ Mutation impact matrix created")
        
        # Test drug resistance landscape
        resistance_data = {
            'drugs': ['Drug_A', 'Drug_B', 'Drug_C'],
            'mutations': [f'Mut_{i}' for i in range(6)],
            'resistance_matrix': np.random.exponential(1, (6, 3))
        }
        
        resistance_fig = research_viz.create_drug_resistance_landscape(resistance_data)
        print("   ✅ Drug resistance landscape created")
        
        # Test evolutionary trajectory
        evolution_data = {
            'time_points': list(range(12)),
            'variants': ['Alpha', 'Beta', 'Delta', 'Omicron'],
            'frequencies': {}
        }
        
        evolution_fig = research_viz.create_evolutionary_trajectory(evolution_data)
        print("   ✅ Evolutionary trajectory created")
        
        # Test network analysis
        network_data = {
            'nodes': [f'Node_{i}' for i in range(8)],
            'edges': [],
            'node_properties': np.random.uniform(0, 1, 8)
        }
        
        network_fig = research_viz.create_network_analysis(network_data)
        print("   ✅ Network analysis created")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Research visualizations test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧬 Virus Mutation Simulation - Mutation Tree Fix Test")
    print("=" * 55)
    
    # Run tests
    tree_test = test_mutation_tree_visualization()
    viz_test = test_other_visualizations()
    research_test = test_research_visualizations()
    
    print("\n📊 Test Summary")
    print("=" * 20)
    print(f"Mutation Tree Fix: {'✅ PASS' if tree_test else '❌ FAIL'}")
    print(f"Other Visualizations: {'✅ PASS' if viz_test else '❌ FAIL'}")
    print(f"Research Visualizations: {'✅ PASS' if research_test else '❌ FAIL'}")
    
    if tree_test and viz_test and research_test:
        print("\n🎉 All visualization tests passed!")
        print("\n💡 Fixes applied:")
        print("   • Fixed TypeError: unhashable type 'dict' in mutation tree")
        print("   • Corrected node position access using node IDs")
        print("   • Enhanced error handling for empty data")
        print("   • Verified all advanced visualization methods")
        
        print("\n🚀 Advanced AI insights should now work without errors!")
    else:
        print("\n⚠️  Some tests failed. Check the error messages above.")
        
    print("\n" + "=" * 55)